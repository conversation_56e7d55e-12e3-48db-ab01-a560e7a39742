{"annotations": {"title": "Reprioritize sub-issue", "readOnlyHint": false}, "description": "Reprioritize a sub-issue to a different position in the parent issue's sub-issue list.", "inputSchema": {"properties": {"after_id": {"description": "The ID of the sub-issue to be prioritized after (either after_id OR before_id should be specified)", "type": "number"}, "before_id": {"description": "The ID of the sub-issue to be prioritized before (either after_id OR before_id should be specified)", "type": "number"}, "issue_number": {"description": "The number of the parent issue", "type": "number"}, "owner": {"description": "Repository owner", "type": "string"}, "repo": {"description": "Repository name", "type": "string"}, "sub_issue_id": {"description": "The ID of the sub-issue to reprioritize. ID is not the same as issue number", "type": "number"}}, "required": ["owner", "repo", "issue_number", "sub_issue_id"], "type": "object"}, "name": "reprioritize_sub_issue"}