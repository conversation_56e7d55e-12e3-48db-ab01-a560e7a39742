{"annotations": {"title": "Create branch", "readOnlyHint": false}, "description": "Create a new branch in a GitHub repository", "inputSchema": {"properties": {"branch": {"description": "Name for new branch", "type": "string"}, "from_branch": {"description": "Source branch (defaults to repo default)", "type": "string"}, "owner": {"description": "Repository owner", "type": "string"}, "repo": {"description": "Repository name", "type": "string"}}, "required": ["owner", "repo", "branch"], "type": "object"}, "name": "create_branch"}