{"annotations": {"title": "Get project", "readOnlyHint": true}, "description": "Get Project for a user or org", "inputSchema": {"properties": {"owner": {"description": "If owner_type == user it is the handle for the GitHub user account. If owner_type == org it is the name of the organization. The name is not case sensitive.", "type": "string"}, "owner_type": {"description": "Owner type", "enum": ["user", "org"], "type": "string"}, "project_number": {"description": "The project's number", "type": "number"}}, "required": ["project_number", "owner_type", "owner"], "type": "object"}, "name": "get_project"}