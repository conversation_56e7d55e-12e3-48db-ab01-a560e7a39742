{"annotations": {"title": "List sub-issues", "readOnlyHint": true}, "description": "List sub-issues for a specific issue in a GitHub repository.", "inputSchema": {"properties": {"issue_number": {"description": "Issue number", "type": "number"}, "owner": {"description": "Repository owner", "type": "string"}, "page": {"description": "Page number for pagination (default: 1)", "type": "number"}, "per_page": {"description": "Number of results per page (max 100, default: 30)", "type": "number"}, "repo": {"description": "Repository name", "type": "string"}}, "required": ["owner", "repo", "issue_number"], "type": "object"}, "name": "list_sub_issues"}