{"annotations": {"title": "Create and submit a pull request review without comments", "readOnlyHint": false}, "description": "Create and submit a review for a pull request without review comments.", "inputSchema": {"properties": {"body": {"description": "Review comment text", "type": "string"}, "commitID": {"description": "SHA of commit to review", "type": "string"}, "event": {"description": "Review action to perform", "enum": ["APPROVE", "REQUEST_CHANGES", "COMMENT"], "type": "string"}, "owner": {"description": "Repository owner", "type": "string"}, "pullNumber": {"description": "Pull request number", "type": "number"}, "repo": {"description": "Repository name", "type": "string"}}, "required": ["owner", "repo", "pullNumber", "body", "event"], "type": "object"}, "name": "create_and_submit_pull_request_review"}