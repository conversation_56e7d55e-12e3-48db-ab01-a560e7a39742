{"annotations": {"title": "Remove sub-issue", "readOnlyHint": false}, "description": "Remove a sub-issue from a parent issue in a GitHub repository.", "inputSchema": {"properties": {"issue_number": {"description": "The number of the parent issue", "type": "number"}, "owner": {"description": "Repository owner", "type": "string"}, "repo": {"description": "Repository name", "type": "string"}, "sub_issue_id": {"description": "The ID of the sub-issue to remove. ID is not the same as issue number", "type": "number"}}, "required": ["owner", "repo", "issue_number", "sub_issue_id"], "type": "object"}, "name": "remove_sub_issue"}