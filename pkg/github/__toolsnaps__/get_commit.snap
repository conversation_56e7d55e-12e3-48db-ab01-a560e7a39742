{"annotations": {"title": "Get commit details", "readOnlyHint": true}, "description": "Get details for a commit from a GitHub repository", "inputSchema": {"properties": {"include_diff": {"default": true, "description": "Whether to include file diffs and stats in the response. Default is true.", "type": "boolean"}, "owner": {"description": "Repository owner", "type": "string"}, "page": {"description": "Page number for pagination (min 1)", "minimum": 1, "type": "number"}, "perPage": {"description": "Results per page for pagination (min 1, max 100)", "maximum": 100, "minimum": 1, "type": "number"}, "repo": {"description": "Repository name", "type": "string"}, "sha": {"description": "Commit SHA, branch name, or tag name", "type": "string"}}, "required": ["owner", "repo", "sha"], "type": "object"}, "name": "get_commit"}