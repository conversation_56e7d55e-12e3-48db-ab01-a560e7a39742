{"annotations": {"title": "List projects", "readOnlyHint": true}, "description": "List Projects for a user or org", "inputSchema": {"properties": {"owner": {"description": "If owner_type == user it is the handle for the GitHub user account. If owner_type == org it is the name of the organization. The name is not case sensitive.", "type": "string"}, "owner_type": {"description": "Owner type", "enum": ["user", "org"], "type": "string"}, "per_page": {"description": "Number of results per page (max 100, default: 30)", "type": "number"}, "query": {"description": "Filter projects by a search query (matches title and description)", "type": "string"}}, "required": ["owner_type", "owner"], "type": "object"}, "name": "list_projects"}