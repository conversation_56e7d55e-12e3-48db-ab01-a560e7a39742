{"annotations": {"title": "Manage repository notification subscription", "readOnlyHint": false}, "description": "Manage a repository notification subscription: ignore, watch, or delete repository notifications subscription for the provided repository.", "inputSchema": {"properties": {"action": {"description": "Action to perform: ignore, watch, or delete the repository notification subscription.", "enum": ["ignore", "watch", "delete"], "type": "string"}, "owner": {"description": "The account owner of the repository.", "type": "string"}, "repo": {"description": "The name of the repository.", "type": "string"}}, "required": ["owner", "repo", "action"], "type": "object"}, "name": "manage_repository_notification_subscription"}