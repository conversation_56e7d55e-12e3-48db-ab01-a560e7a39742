{"annotations": {"title": "Get issue comments", "readOnlyHint": true}, "description": "Get comments for a specific issue in a GitHub repository.", "inputSchema": {"properties": {"issue_number": {"description": "Issue number", "type": "number"}, "owner": {"description": "Repository owner", "type": "string"}, "page": {"description": "Page number for pagination (min 1)", "minimum": 1, "type": "number"}, "perPage": {"description": "Results per page for pagination (min 1, max 100)", "maximum": 100, "minimum": 1, "type": "number"}, "repo": {"description": "Repository name", "type": "string"}}, "required": ["owner", "repo", "issue_number"], "type": "object"}, "name": "get_issue_comments"}