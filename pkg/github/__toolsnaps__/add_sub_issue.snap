{"annotations": {"title": "Add sub-issue", "readOnlyHint": false}, "description": "Add a sub-issue to a parent issue in a GitHub repository.", "inputSchema": {"properties": {"issue_number": {"description": "The number of the parent issue", "type": "number"}, "owner": {"description": "Repository owner", "type": "string"}, "replace_parent": {"description": "When true, replaces the sub-issue's current parent issue", "type": "boolean"}, "repo": {"description": "Repository name", "type": "string"}, "sub_issue_id": {"description": "The ID of the sub-issue to add. ID is not the same as issue number", "type": "number"}}, "required": ["owner", "repo", "issue_number", "sub_issue_id"], "type": "object"}, "name": "add_sub_issue"}