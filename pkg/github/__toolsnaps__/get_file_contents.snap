{"annotations": {"title": "Get file or directory contents", "readOnlyHint": true}, "description": "Get the contents of a file or directory from a GitHub repository", "inputSchema": {"properties": {"owner": {"description": "Repository owner (username or organization)", "type": "string"}, "path": {"default": "/", "description": "Path to file/directory (directories must end with a slash '/')", "type": "string"}, "ref": {"description": "Accepts optional git refs such as `refs/tags/{tag}`, `refs/heads/{branch}` or `refs/pull/{pr_number}/head`", "type": "string"}, "repo": {"description": "Repository name", "type": "string"}, "sha": {"description": "Accepts optional commit SHA. If specified, it will be used instead of ref", "type": "string"}}, "required": ["owner", "repo"], "type": "object"}, "name": "get_file_contents"}