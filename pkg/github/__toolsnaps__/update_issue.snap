{"annotations": {"title": "Edit issue", "readOnlyHint": false}, "description": "Update an existing issue in a GitHub repository.", "inputSchema": {"properties": {"assignees": {"description": "New assignees", "items": {"type": "string"}, "type": "array"}, "body": {"description": "New description", "type": "string"}, "duplicate_of": {"description": "Issue number that this issue is a duplicate of. Only used when state_reason is 'duplicate'.", "type": "number"}, "issue_number": {"description": "Issue number to update", "type": "number"}, "labels": {"description": "New labels", "items": {"type": "string"}, "type": "array"}, "milestone": {"description": "New milestone number", "type": "number"}, "owner": {"description": "Repository owner", "type": "string"}, "repo": {"description": "Repository name", "type": "string"}, "state": {"description": "New state", "enum": ["open", "closed"], "type": "string"}, "state_reason": {"description": "Reason for the state change. Ignored unless state is changed.", "enum": ["completed", "not_planned", "duplicate"], "type": "string"}, "title": {"description": "New title", "type": "string"}, "type": {"description": "New issue type", "type": "string"}}, "required": ["owner", "repo", "issue_number"], "type": "object"}, "name": "update_issue"}