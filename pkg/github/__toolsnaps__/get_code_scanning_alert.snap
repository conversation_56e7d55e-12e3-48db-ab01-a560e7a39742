{"annotations": {"title": "Get code scanning alert", "readOnlyHint": true}, "description": "Get details of a specific code scanning alert in a GitHub repository.", "inputSchema": {"properties": {"alertNumber": {"description": "The number of the alert.", "type": "number"}, "owner": {"description": "The owner of the repository.", "type": "string"}, "repo": {"description": "The name of the repository.", "type": "string"}}, "required": ["owner", "repo", "alertNumber"], "type": "object"}, "name": "get_code_scanning_alert"}