{"annotations": {"title": "Delete the requester's latest pending pull request review", "readOnlyHint": false}, "description": "Delete the requester's latest pending pull request review. Use this after the user decides not to submit a pending review, if you don't know if they already created one then check first.", "inputSchema": {"properties": {"owner": {"description": "Repository owner", "type": "string"}, "pullNumber": {"description": "Pull request number", "type": "number"}, "repo": {"description": "Repository name", "type": "string"}}, "required": ["owner", "repo", "pullNumber"], "type": "object"}, "name": "delete_pending_pull_request_review"}