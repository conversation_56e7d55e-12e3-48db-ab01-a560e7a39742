{"annotations": {"title": "Get tag details", "readOnlyHint": true}, "description": "Get details about a specific git tag in a GitHub repository", "inputSchema": {"properties": {"owner": {"description": "Repository owner", "type": "string"}, "repo": {"description": "Repository name", "type": "string"}, "tag": {"description": "Tag name", "type": "string"}}, "required": ["owner", "repo", "tag"], "type": "object"}, "name": "get_tag"}