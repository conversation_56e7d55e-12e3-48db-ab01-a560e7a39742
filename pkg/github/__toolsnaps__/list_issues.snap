{"annotations": {"title": "List issues", "readOnlyHint": true}, "description": "List issues in a GitHub repository. For pagination, use the 'endCursor' from the previous response's 'pageInfo' in the 'after' parameter.", "inputSchema": {"properties": {"after": {"description": "Cursor for pagination. Use the endCursor from the previous page's PageInfo for GraphQL APIs.", "type": "string"}, "direction": {"description": "Order direction. If provided, the 'orderBy' also needs to be provided.", "enum": ["ASC", "DESC"], "type": "string"}, "labels": {"description": "Filter by labels", "items": {"type": "string"}, "type": "array"}, "orderBy": {"description": "Order issues by field. If provided, the 'direction' also needs to be provided.", "enum": ["CREATED_AT", "UPDATED_AT", "COMMENTS"], "type": "string"}, "owner": {"description": "Repository owner", "type": "string"}, "perPage": {"description": "Results per page for pagination (min 1, max 100)", "maximum": 100, "minimum": 1, "type": "number"}, "repo": {"description": "Repository name", "type": "string"}, "since": {"description": "Filter by date (ISO 8601 timestamp)", "type": "string"}, "state": {"description": "Filter by state, by default both open and closed issues are returned when not provided", "enum": ["OPEN", "CLOSED"], "type": "string"}}, "required": ["owner", "repo"], "type": "object"}, "name": "list_issues"}