{"annotations": {"title": "Get pull request review comments", "readOnlyHint": true}, "description": "Get pull request review comments. They are comments made on a portion of the unified diff during a pull request review. These are different from commit comments and issue comments in a pull request.", "inputSchema": {"properties": {"owner": {"description": "Repository owner", "type": "string"}, "pullNumber": {"description": "Pull request number", "type": "number"}, "repo": {"description": "Repository name", "type": "string"}}, "required": ["owner", "repo", "pullNumber"], "type": "object"}, "name": "get_pull_request_review_comments"}