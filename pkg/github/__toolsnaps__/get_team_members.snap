{"annotations": {"title": "Get team members", "readOnlyHint": true}, "description": "Get member usernames of a specific team in an organization. Limited to organizations accessible with current credentials", "inputSchema": {"properties": {"org": {"description": "Organization login (owner) that contains the team.", "type": "string"}, "team_slug": {"description": "Team slug", "type": "string"}}, "required": ["org", "team_slug"], "type": "object"}, "name": "get_team_members"}