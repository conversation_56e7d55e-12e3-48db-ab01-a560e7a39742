{"annotations": {"title": "Delete file", "readOnlyHint": false, "destructiveHint": true}, "description": "Delete a file from a GitHub repository", "inputSchema": {"properties": {"branch": {"description": "Branch to delete the file from", "type": "string"}, "message": {"description": "Commit message", "type": "string"}, "owner": {"description": "Repository owner (username or organization)", "type": "string"}, "path": {"description": "Path to the file to delete", "type": "string"}, "repo": {"description": "Repository name", "type": "string"}}, "required": ["owner", "repo", "path", "message", "branch"], "type": "object"}, "name": "delete_file"}