{"annotations": {"title": "List dependabot alerts", "readOnlyHint": true}, "description": "List dependabot alerts in a GitHub repository.", "inputSchema": {"properties": {"owner": {"description": "The owner of the repository.", "type": "string"}, "repo": {"description": "The name of the repository.", "type": "string"}, "severity": {"description": "Filter dependabot alerts by severity", "enum": ["low", "medium", "high", "critical"], "type": "string"}, "state": {"default": "open", "description": "Filter dependabot alerts by state. Defaults to open", "enum": ["open", "fixed", "dismissed", "auto_dismissed"], "type": "string"}}, "required": ["owner", "repo"], "type": "object"}, "name": "list_dependabot_alerts"}