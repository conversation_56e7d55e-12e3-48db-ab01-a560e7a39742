{"annotations": {"title": "Create or update file", "readOnlyHint": false}, "description": "Create or update a single file in a GitHub repository. If updating, you must provide the SHA of the file you want to update. Use this tool to create or update a file in a GitHub repository remotely; do not use it for local file operations.", "inputSchema": {"properties": {"branch": {"description": "Branch to create/update the file in", "type": "string"}, "content": {"description": "Content of the file", "type": "string"}, "message": {"description": "Commit message", "type": "string"}, "owner": {"description": "Repository owner (username or organization)", "type": "string"}, "path": {"description": "Path where to create/update the file", "type": "string"}, "repo": {"description": "Repository name", "type": "string"}, "sha": {"description": "Required if updating an existing file. The blob SHA of the file being replaced.", "type": "string"}}, "required": ["owner", "repo", "path", "content", "message", "branch"], "type": "object"}, "name": "create_or_update_file"}