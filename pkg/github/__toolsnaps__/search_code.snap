{"annotations": {"title": "Search code", "readOnlyHint": true}, "description": "Fast and precise code search across ALL GitHub repositories using GitHub's native search engine. Best for finding exact symbols, functions, classes, or specific code patterns.", "inputSchema": {"properties": {"order": {"description": "Sort order for results", "enum": ["asc", "desc"], "type": "string"}, "page": {"description": "Page number for pagination (min 1)", "minimum": 1, "type": "number"}, "perPage": {"description": "Results per page for pagination (min 1, max 100)", "maximum": 100, "minimum": 1, "type": "number"}, "query": {"description": "Search query using GitHub's powerful code search syntax. Examples: 'content:Skill language:Java org:github', 'NOT is:archived language:Python OR language:go', 'repo:github/github-mcp-server'. Supports exact matching, language filters, path filters, and more.", "type": "string"}, "sort": {"description": "Sort field ('indexed' only)", "type": "string"}}, "required": ["query"], "type": "object"}, "name": "search_code"}