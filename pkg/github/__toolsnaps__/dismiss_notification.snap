{"annotations": {"title": "Dismiss notification", "readOnlyHint": false}, "description": "Dismiss a notification by marking it as read or done", "inputSchema": {"properties": {"state": {"description": "The new state of the notification (read/done)", "enum": ["read", "done"], "type": "string"}, "threadID": {"description": "The ID of the notification thread", "type": "string"}}, "required": ["threadID"], "type": "object"}, "name": "dismiss_notification"}