{"annotations": {"title": "List pull requests", "readOnlyHint": true}, "description": "List pull requests in a GitHub repository. If the user specifies an author, then DO NOT use this tool and use the search_pull_requests tool instead.", "inputSchema": {"properties": {"base": {"description": "Filter by base branch", "type": "string"}, "direction": {"description": "Sort direction", "enum": ["asc", "desc"], "type": "string"}, "head": {"description": "Filter by head user/org and branch", "type": "string"}, "owner": {"description": "Repository owner", "type": "string"}, "page": {"description": "Page number for pagination (min 1)", "minimum": 1, "type": "number"}, "perPage": {"description": "Results per page for pagination (min 1, max 100)", "maximum": 100, "minimum": 1, "type": "number"}, "repo": {"description": "Repository name", "type": "string"}, "sort": {"description": "Sort by", "enum": ["created", "updated", "popularity", "long-running"], "type": "string"}, "state": {"description": "Filter by state", "enum": ["open", "closed", "all"], "type": "string"}}, "required": ["owner", "repo"], "type": "object"}, "name": "list_pull_requests"}