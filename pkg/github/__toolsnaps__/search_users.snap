{"annotations": {"title": "Search users", "readOnlyHint": true}, "description": "Find GitHub users by username, real name, or other profile information. Useful for locating developers, contributors, or team members.", "inputSchema": {"properties": {"order": {"description": "Sort order", "enum": ["asc", "desc"], "type": "string"}, "page": {"description": "Page number for pagination (min 1)", "minimum": 1, "type": "number"}, "perPage": {"description": "Results per page for pagination (min 1, max 100)", "maximum": 100, "minimum": 1, "type": "number"}, "query": {"description": "User search query. Examples: 'john smith', 'location:seattle', 'followers:>100'. Search is automatically scoped to type:user.", "type": "string"}, "sort": {"description": "Sort users by number of followers or repositories, or when the person joined GitHub.", "enum": ["followers", "repositories", "joined"], "type": "string"}}, "required": ["query"], "type": "object"}, "name": "search_users"}