name: Close inactive issues
on:
  schedule:
    - cron: "30 8 * * *"

jobs:
  close-issues:
    runs-on: ubuntu-latest
    env:
      PR_DAYS_BEFORE_STALE: 60
      PR_DAYS_BEFORE_CLOSE: 120
      PR_STALE_LABEL: stale
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@v10
        with:
          days-before-issue-stale: ${{ env.PR_DAYS_BEFORE_STALE }}
          days-before-issue-close: ${{ env.PR_DAYS_BEFORE_CLOSE }}
          stale-issue-label: ${{ env.PR_STALE_LABEL }}
          stale-issue-message: "This issue is stale because it has been open for ${{ env.PR_DAYS_BEFORE_STALE }} days with no activity. Leave a comment to avoid closing this issue in ${{ env.PR_DAYS_BEFORE_CLOSE }} days."
          close-issue-message: "This issue was closed because it has been inactive for ${{ env.PR_DAYS_BEFORE_CLOSE }} days since being marked as stale."
          days-before-pr-stale: -1
          days-before-pr-close: -1
          # Start with the oldest items first
          ascending: true
          repo-token: ${{ secrets.GITHUB_TOKEN }}
