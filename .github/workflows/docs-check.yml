name: Documentation Check

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

permissions:
  contents: read

jobs:
  docs-check:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v5

    - name: Set up Go
      uses: actions/setup-go@v6
      with:
        go-version-file: 'go.mod'

    - name: Build docs generator
      run: go build -o github-mcp-server ./cmd/github-mcp-server

    - name: Generate documentation
      run: ./github-mcp-server generate-docs

    - name: Check for documentation changes
      run: |
        if ! git diff --exit-code README.md; then
          echo "❌ Documentation is out of date!"
          echo ""
          echo "The generated documentation differs from what's committed."
          echo "Please run the following command to update the documentation:"
          echo ""
          echo "  go run ./cmd/github-mcp-server generate-docs"
          echo ""
          echo "Then commit the changes."
          echo ""
          echo "Changes detected:"
          git diff README.md
          exit 1
        else
          echo "✅ Documentation is up to date!"
        fi
