# Create a github action that runs the license check script and fails if it exits with a non-zero status

name: License Check
on: [push, pull_request]
permissions:
  contents: read

jobs:
  license-check:
    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v5

      - name: Set up Go
        uses: actions/setup-go@v6
        with:
          go-version-file: "go.mod"
      - name: check licenses
        run: ./script/licenses-check
