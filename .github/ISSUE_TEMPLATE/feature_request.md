---
name: "⭐ Submit a feature request"
about: Surface a feature or problem that you think should be solved
title: ''
labels: enhancement
assignees: ''

---

### Describe the feature or problem you’d like to solve

A clear and concise description of what the feature or problem is.

### Proposed solution

How will it benefit GitHub MCP Server and its users?

### Example prompts or workflows (for tools/toolsets only)

If it's a new tool or improvement, share 3–5 example prompts or workflows it would enable. Just enough detail to show the value. Clear, valuable use cases are more likely to get approved.

### Additional context

Add any other context like screenshots or mockups are helpful, if applicable.
