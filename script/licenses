#!/bin/bash

go install github.com/google/go-licenses@latest

rm -rf third-party
mkdir -p third-party
export TEMPDIR="$(mktemp -d)"

trap "rm -fr ${TEMPDIR}" EXIT

for goos in linux darwin windows ; do
    # Note: we ignore warnings because we want the command to succeed, however the output should be checked
    #       for any new warnings, and potentially we may need to add license information. 
    #
    #       Normally these warnings are packages containing non go code, which may or may not require explicit attribution,
    #       depending on the license.
    GOOS="${goos}" go-licenses save ./... --save_path="${TEMPDIR}/${goos}" --force || echo "Ignore warnings"
    GOOS="${goos}" go-licenses report ./... --template .github/licenses.tmpl > third-party-licenses.${goos}.md || echo "Ignore warnings"
    cp -fR "${TEMPDIR}/${goos}"/* third-party/
done

